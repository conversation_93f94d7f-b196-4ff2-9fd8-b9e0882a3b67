package com.ecco.dto;

import static java.util.stream.Collectors.toList;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import javax.annotation.Nonnull;
import javax.validation.constraints.NotNull;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import org.springframework.util.Assert;


public class ServiceProjectsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private final ServiceAclId restrictedService;
    private final List<ServiceCategorisationViewModel> restrictedProjects;
    private final boolean serviceHasProjects;

    /** Only restrict by service (?? - contract needs firming up here) */
    public ServiceProjectsDto(@Nonnull ServiceAclId restrictedService) {
        this(restrictedService, false, Collections.emptyList());
    }

    /**
     * Created from ServicesProjectsDto which comes from the determining access from the database
     * NB These are domain objects.
     */
    public ServiceProjectsDto(@Nonnull ServiceAclId restrictedService, boolean serviceHasProjects, @Nonnull List<ServiceCategorisationViewModel> restrictedProjects) {
        this.restrictedService = restrictedService;
        this.restrictedProjects = restrictedProjects;
        Assert.notNull(restrictedService);
        Assert.notNull(restrictedProjects, "projects can be empty but not null - might as well set some expectations for consistency");
        this.serviceHasProjects = serviceHasProjects;
    }

    public ServiceAclId getRestrictedService() {
        return restrictedService;
    }

    /**
     * This call is used in cases which don't expect null projects
     * (eg, 'hasProjectRestrictions' has already been determined)
     */
    public @NotNull Collection<ProjectAclId> getRestrictedProjects() {
        return this.getRestrictedProjects(true);
    }
    public Collection<ProjectAclId> getRestrictedProjectsAndNullProjects() {
        return this.getRestrictedProjects(false);
    }
    private Collection<ProjectAclId> getRestrictedProjects(boolean nullable) {
        return restrictedProjects.stream()
                .filter(p -> nullable || (p.getProjectId() != null))
                .map(p -> p.getProjectId() == null ? null : new ProjectAclId(p.getProjectId(), p.getProjectName()))
                .collect(toList());
    }

    public boolean isServiceHasProjects() {
        return serviceHasProjects;
    }

    @Override
    public String toString() {
        return "service: " + restrictedService.getId() + ", projects " + restrictedProjects + ", serviceHasProjects: " + serviceHasProjects;
    }
}
