import $ = require("jquery");
import _ = require("lodash");
import BaseControl = require("../controls/BaseControl");
import ErrorControl = require("../controls/ErrorControl");
import CheckboxButton = require("../controls/input/CheckboxButton");
import ViewSignatureControl = require("../controls/ViewSignatureControl");
import EvidenceCommentForm = require("./EvidenceCommentForm");
import {EccoDate, EccoDateTime, SparseArray} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {BaseEvidenceBasedWork, Command, CommandQueue, EvidenceDiscriminator} from "ecco-commands";
import {apiClient} from "ecco-components";
import * as referralDtos from "ecco-dto";
import {
    DeleteType,
    FlagEvidenceDto,
    isOffline,
    ListDefinitionEntry,
    ReferralAjaxRepository,
    SmartStepStatus,
    statusMessages,
    SupportAction
} from "ecco-dto";
import {BaseOutcomeBasedWork as BaseOutcomeBasedWorkDto, DeleteEvidenceRequestCommandDto} from "ecco-dto/evidence-dto";
import {EvidenceDef, EvidenceHistoryFilter, HierarchyPosition} from "ecco-evidence";
import {AttachmentsControl} from "../attachments/attachments";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {baseURI} from "../environment";
import {getCommandQueueRepository} from "ecco-offline-data";
import {ScheduleData} from "ecco-rota";

type DomainType = BaseEvidenceBasedWork<BaseOutcomeBasedWorkDto>;

const referralAjaxRepository = new ReferralAjaxRepository(apiClient);
// ** ONLINE ONLY **

function formatDateTimeShortNoMidnight(dateTime: EccoDateTime): string {
    if (dateTime.isMidnight()) {
        return dateTime.format("ddd, D MMM YYYY");
    }
    return dateTime.format("ddd, D MMM YYYY HH:mm");
}

export abstract class BaseHistoryItemControl<WORK extends DomainType> extends BaseControl {

    private $comment: $.JQuery;
    private $menu: $.JQuery;

    private signCheckbox: CheckboxButton;

    private latestRequestDeleteCommand: DeleteEvidenceRequestCommandDto;

    /** onlineServiceTypes is provided only when being used online in 'admin mode' */
    constructor(protected discriminator: EvidenceDiscriminator,
                protected serviceRecipient: referralDtos.ServiceRecipientWithEntities,
                protected work: WORK,
                protected deleteHandler?: (cmd: Command) => void) {
        super($("<li>").addClass("support-history-item"));
    }

    protected render() {
        try {
            this.renderInternal();
        } catch (e) {
            console.error("error: %o", e);
            this.element().empty()
                // .append($("<pre>").addClass("alert alert-danger").text(e.toString()));
                .append(new ErrorControl("could not load data")
                    .exception(e)
                    .element())
        }
    }
    protected abstract renderInternal(): void;

    protected editComment() {
        function onSubmit(form: EvidenceCommentForm) {
            let cmdQueue = new CommandQueue(getCommandQueueRepository());
            form.emitChangesTo(cmdQueue);
            return cmdQueue.flushCommands();
        }

        const dto = this.work.getDto();
        const evidenceDef = EvidenceDef.fromTaskName(this.serviceRecipient.features,
                this.serviceRecipient.configResolver.getServiceType(), this.work.getDto().taskName);
        let form = new EvidenceCommentForm(this.serviceRecipient,
            evidenceDef, () => onSubmit(form),
            () => Uuid.parse(dto.id),
            dto);
        showFormInModalDom(form);
    }

    protected requestDelete(revoke: boolean) {
        const evidenceDef = EvidenceDef.fromTaskName(this.serviceRecipient.features,
                this.serviceRecipient.configResolver.getServiceType(), this.work.getDto().taskName);
        let deleteOp = revoke ? DeleteType.REVOKE : DeleteType.REQUEST;
        const dto = this.work.getDto();
        import("../evidence/DeleteEvidenceForm").then(({DeleteEvidenceForm}) => {
            DeleteEvidenceForm.showInModal(this.discriminator, deleteOp, this.serviceRecipient.serviceRecipientId,
                                           this.work.getUuid(), evidenceDef.getEvidenceGroup(), evidenceDef.getTaskName(), JSON.stringify(dto),
                                           this.deleteHandler);
        });
    }

    protected delete() {
        const evidenceDef = EvidenceDef.fromTaskName(this.serviceRecipient.features,
                this.serviceRecipient.configResolver.getServiceType(), this.work.getDto().taskName);
        const dto = this.work.getDto();
        import("../evidence/DeleteEvidenceForm").then(({DeleteEvidenceForm}) => {
            DeleteEvidenceForm.showInModal(null, DeleteType.DELETE, this.serviceRecipient.serviceRecipientId,
                                           this.work.getUuid(), evidenceDef.getEvidenceGroup(), evidenceDef.getTaskName(), JSON.stringify(dto),
                                           this.deleteHandler,
                                           this.latestRequestDeleteCommand.uuid, this.latestRequestDeleteCommand.reason);
        });
    }

    /**
     * Show/hide the evidence according to the filter
     * @return whether the item control is left showing
     */
    public filterEvidence(filter: EvidenceHistoryFilter): boolean {
        // enforce this regardless of the useFitler status
        // because its used internally
        let show = true;
        if (filter.filterOutSignaturesOverride) {
            show = !this.isSigned();
        }
        this.element().toggle(show);

        // no confusion - if we aren't using the filter, exit
        if (!filter.useFilter) {
            return true;
        }

        if (show && filter.filterTaskName) {
            show = this.work.getDto().taskName == filter.filterTaskName;
        }

        if (show && filter.filterCommentTypeName) {
            const sessionData = this.serviceRecipient.features;
            const type = sessionData.getListDefinitionEntryById(this.work.getDto().commentTypeId).getDisplayName();
            show = type == filter.filterCommentTypeName;
        }

        if (show && filter.filterFromDate) {
            let dt = EccoDateTime.parseIso8601(this.work.getDto().workDate);
            show = dt.laterThanOrEqual(filter.filterFromDate.toDateTimeMidnight());
        }
        if (show && filter.filterToDate) {
            let dt = EccoDateTime.parseIso8601(this.work.getDto().workDate);
            show = dt.earlierThanOrEqual(filter.filterToDate.toDateTimeMidnight());
        }

        if (show && filter.filterAttachments) {
            show = this.work.getDto().attachments && this.work.getDto().attachments.length > 0;
        }

        this.element().toggle(show);
        return show;
    }

    public isSigned(): boolean {
        return this.work.getDto().signatureId !== null;
    }

    public showSignButton(show: boolean) {
        if (!this.work.getDto().signatureId) {
            var $sign = this.$comment.find(".unsigned");

            if (!this.signCheckbox) {
                this.signCheckbox = new CheckboxButton("sign", false);
                $sign.append(this.signCheckbox.element());
            }
            $sign.toggle(show);
        }
    }

    public isSelectedToSign(): boolean {
        return this.signCheckbox.isChecked();
    }

    applyUpdate(change: (object: WORK) => WORK) {
        this.work = change(this.work);
        this.render();
        this.element().addClass("updated");
    }

    public getWorkUuid(): string {
        return this.work.getDto().id;
    }

    // see http://javascript.boxsheep.com/how-to-javascript/How-to-check-if-a-value-is-a-number/
    private numberExists(n: number) {
        return isFinite(n) && (+n === n);
    }

    protected renderHeader() {
        const dto = this.work.getDto();
        const workNotFromThisFile = this.serviceRecipient.serviceRecipientId != dto.serviceRecipientId;

        let workDateTime = EccoDateTime.parseIso8601(dto.workDate);
        const unknownWorkDateTime = EccoDateTime.parseIso8601("1900-01-01T00:00:00.0000");
        if (unknownWorkDateTime.equals(workDateTime)) {
            workDateTime = null;
        }

        // service-line: To stand out, we have a separate line for serviceCategorisation if different from the current
        if (workNotFromThisFile) {
            // NB serviceAllocationId doesn't exist on the dto if we use effects - see CommentCommandBuilder
            const workServiceCategorisationName = this.serviceRecipient.features.getServiceCategorisationName(dto.serviceAllocationId);
            let $serviceLine = $("<div>").addClass("clearfix");
            $serviceLine.appendTo(this.element());
            $serviceLine.append(
                $("<span>").addClass('pull-right header-item')
                    // NB could open the file using nav/sr/{serviceRecipientId}
                    .text("from " + workServiceCategorisationName)
            );
        }

        // line: workdate / mins spent / [type]        source / author (hover for created date)
        const $line1 = $("<div>").addClass("clearfix");
        $line1.appendTo(this.element());
        $line1
            .append( $("<span>").addClass('uuid').text(dto.id).hide() )
            .append( $("<span>").addClass('uuid btn btn-link').text("edit work")
                .hide()
                .click( () => this.editComment() )
            )
            .append( $("<span>").addClass("pull-left header-item")
                .text(workDateTime && formatDateTimeShortNoMidnight(workDateTime)) );
        if (this.numberExists(dto.minsSpent) && dto.minsSpent != 0) {
            $line1.append(
                $("<span>").addClass('mins-spent')
                    .append( $("<em>").text(" took " + dto.minsSpent.toString() + " min") ) );
        }
        if (dto.commentTypeId) {
            const sessionData = this.serviceRecipient.features;
            const type = sessionData.getListDefinitionEntryById(this.work.getDto().commentTypeId).getDisplayName();
            $line1.append( $("<span>").text(" [" + type + "]") );
        }

        this.$menu = this.createMenuContainer();

        // Created date is UTC - show this as local time when users hovers for audit
        var auditDateTime = EccoDateTime.parseIso8601Utc(dto.createdDate);
        var auditDateTimeStr = auditDateTime.format("ddd, D MMM YYYY HH:mm");
        let $audit = $("<span>").attr("title", `${auditDateTimeStr} UTC`)
                .append(this.serviceRecipient.configResolver.getServiceType().lookupTaskName(dto.taskName) + " by ")
                .append( $("<em>").text(dto.authorDisplayName + " ") );

        $line1.append($("<span>").addClass("pull-right").append(this.$menu));
        $line1.append($("<span>").addClass("pull-right header-item").append($audit));
    }

    public hideMenu() {
        this.$menu.hide();
    }

    private createMenuContainer(): $.JQuery {
        let $menuDropDown = $("<ul>")
            .addClass("dropdown-menu dropdown-menu-right")
            .attr("role", "menu");
            // .append($("<li>")
            //     .addClass("dropdown-header")
            //     .attr("role", "presentation")
            //     .text("some title in the drop down"))
        if (this.discriminator == EvidenceDiscriminator.FORM) {
            $menuDropDown.append(this.createOpenAsAtButton());
            $menuDropDown.append(this.createOpenToSignedButton());
        }

        $menuDropDown.append(this.createEditButton());
        let $deleteRequest = this.createDeleteButton(DeleteType.REQUEST).hide();
        let $deleteRevoke = this.createDeleteButton(DeleteType.REVOKE).hide();
        let $delete = this.createDeleteButton(DeleteType.DELETE).hide();
        $menuDropDown.append($deleteRequest.hide());
        $menuDropDown.append($deleteRevoke.hide());
        $menuDropDown.append($delete.hide());

        let $menu = $("<span>")
            .addClass("dropdown")
            .append($("<button>") // 'a' gives a blue link
                .addClass("btn btn-default dropdown-toggle")
                .attr("type", "button")
                .attr("data-toggle", "dropdown")
                    .append($("<i>").addClass("fa fa-ellipsis-v")))
                    // .append($("<span>")
                    //     .text("admin")
                    //     .append($("<span>").addClass("caret")))) // fa fa-caret-down
            .append($menuDropDown);

        // add the delete/request when online (could allow offline if downloaded the latest delete requests)
        if (!isOffline()) {
            $menu.click((event: $.JQueryEventObject) => {
                this.showDeleteMenuItems($deleteRequest, $deleteRevoke, $delete);
            });
        }
        return $menu;
    }

    private showDeleteMenuItems($deleteRequest: $.JQuery, $deleteRevoke: $.JQuery, $delete: $.JQuery) {
        const deleteOptions = this.serviceRecipient.features.isEnabled("support.evidence.delete");
        if (deleteOptions) {
            referralAjaxRepository.findLatestEvidenceDeleteRequestForWorkUuid(this.serviceRecipient.serviceRecipientId, this.getWorkUuid())
                .then(latestRequestCommand => {
                    this.latestRequestDeleteCommand = latestRequestCommand;
                    $deleteRequest.toggle(latestRequestCommand == null || latestRequestCommand.revoke);
                    $deleteRevoke.toggle(latestRequestCommand != null && !latestRequestCommand.revoke);
                    $delete.toggle(latestRequestCommand != null && !latestRequestCommand.revoke && this.serviceRecipient.features.hasRoleEvidenceAdmin());
                });
        }
    }

    protected createOpenAsAtButton(): $.JQuery {

        let $item = $("<li>")
            .append($("<a>")
                .attr("role", "button")
                .addClass("menu-action-printable")
                .append($("<span>").addClass("fa fa-pencil"))
                .append($("<span>").text("  open to this version"))
            );
        $item.click(() => {
            const dto = this.work.getDto();
            const search = new URLSearchParams(
                {
                    taskName: dto.taskName,
                    workUuid: this.work.getUuid().toString()
                });
            const $url = `${baseURI}form-snapshot/svcrec/${dto.serviceRecipientId}?${search}`;
            const win = window.open($url.toString(), '_blank');
            win.focus();
        });
        return $item;
    }

    protected createOpenToSignedButton(): $.JQuery {

        let $item = $("<li>")
            .append($("<a>")
                .attr("role", "button")
                .addClass("menu-action-printable")
                .append($("<span>").addClass("fa fa-pencil"))
                .append($("<span>").text("  open to signed version"))
            );
        $item.click(() => {
            const dto = this.work.getDto();
            const search = new URLSearchParams(
                {
                    taskName: dto.taskName,
                    workUuid: this.work.getUuid().toString(),
                    fromLastSignedSnapshot: "true"
                });
            const $url = `${baseURI}form-snapshot/svcrec/${dto.serviceRecipientId}?${search}`;
            const win = window.open($url.toString(), '_blank');
            win.focus();
        });
        return $item;
    }

    private createEditButton(): $.JQuery | null {
        // permissions currently are:
        //  - editing: staff, admin evidence [ROLE_EDITEVIDENCE]
        //  - deleting: managers, admin evidence [ROLE_ADMINEVIDENCE]
        // For larger orgs, perhaps remove relevant groups_authorities for staff/managers, and expose 'admin evidence' through setting id=46
        if (this.serviceRecipient.features.isEnabled("evidence.history.editComment")) {
            let canEdit = this.serviceRecipient.features.hasRoleEvidenceAdmin();
            if (this.serviceRecipient.features.hasRoleEditEvidence() && (this.work.getDto().signatureId == null)) {
                canEdit = true;
            }

            if (canEdit) {
                let $item = $("<li>")
                    .append($("<a>")
                        .attr("role", "button")
                        .addClass("menu-action-edit")
                        .append($("<span>").addClass("fa fa-pencil"))
                        .append($("<span>").text("  edit"))
                    );
                $item.click(() => this.editComment());
                return $item;
            }
        }
        return null;
    }

    private createDeleteButton(deleteOp: DeleteType): $.JQuery {
        let clazz = "";
        let label = "";
        let handler = () => {};
        switch (deleteOp) {
            case DeleteType.REQUEST:
                clazz = "menu-action-deleteRequest";
                label = "request deletion";
                handler = () => this.requestDelete(false);
                break;
            case DeleteType.REVOKE:
                clazz = "menu-action-deleteRevoke";
                label = "revoke requested deletion";
                handler = () => this.requestDelete(true);
                break;
            case DeleteType.DELETE:
                clazz = "menu-action-delete";
                label = "delete";
                handler = () => this.delete();
                break;
        }
        const $link = $("<a>")
                .attr("role", "button")
                .addClass(clazz)
                .append($("<span>").addClass("fa fa-trash-o"))
                .append($("<span>").text("  " + label));
        $link.click(handler);
        return $("<li>").append($link);
    }

    protected renderComment($suffixSpace: $.JQuery) {

        let $signatureSpace = $("<div>");
        $suffixSpace.append($("<div>").css({"width":"80%", "margin":"auto"}).append($signatureSpace));

        // line: comment and 'show signature'
        this.$comment = $("<div>")
            .addClass("comment clearfix")
            .text(this.work.getDto().comment || "(no comment recorded)");
        if (this.work.getDto().signatureId) {
            this.$comment.append(isOffline()
                ? $("<span>").addClass("signature pull-right").text("signed")
                : ViewSignatureControl.getLinkForSignatureId(this.work.getDto().signatureId, $signatureSpace));
        }
        else {
                // hidden element for enhancing with checkbox input when signing work
            this.$comment.append( $("<span>")
                .addClass("unsigned pull-right") );
        }
        this.element().append(this.$comment);
    }

    protected renderAttachments(inline = false) {
        const dto = this.work.getDto();
        if (dto.attachments && dto.attachments.length > 0) {
            try {
                let $files = AttachmentsControl.createAttachedContainer()
                    .appendTo(this.element());
                // Use parent as we're one of the controls being searched for - rather HACKy but not exactly components at that end
                AttachmentsControl.attachedFilesLoaded($files.parent(), dto.attachments, inline);
            } catch(e) {
                // HACK for attachments = undefined on iPad
                console.log(e);
            }
        }
    }

    // NB SupportAction is risk also - see RiskActionEvidenceDto
    protected renderActions(actions: SupportAction[]) {
        // line 3: smart step info
        const $list = $("<ul>").addClass("list-unstyled actions-detail");
        actions && $list.append(actions.map((action, i) => {
            const $entry = $("<li>").css("padding-bottom", "10px")
            if (i > 0) {
                $entry.prepend($("<hr>").width("90%").css("margin", "auto"));
            }

            // See com.ecco.webApi.evidence.BaseGoalCommandHandler.applyCommonUpdates for conditions for this getting set
            return (action.status == SmartStepStatus.AchievedAndStillRelevant)
                    ? this.renderCheck(action, $entry)
                    : this.renderAction(action, $entry);
        }));
        return $list;
    }

    private renderCheck(action: SupportAction, $entry) {
        const $action = $("<div>");
        const goalName = action.goalName ? action.goalName : null;
        const actionName = this.describeAction(action.actionId);
        const $title = $("<span>").text(goalName || actionName || "-");

        const sessionData = this.serviceRecipient.features;
        let statusChangeReasonIconClass = sessionData
                .getListDefinitionEntryById(action.statusChangeReasonId).getIconClasses();
        $action.append( $("<i>")
                .addClass("fa selected")
                .addClass(statusChangeReasonIconClass) );
        $action.append($title);

        $entry.append($action);
        return $entry;
    }

    private renderAction(action: SupportAction, $entry) {

        const $iconArea = $("<div>");


        const hasStatusChange = action.statusChange; // this had additional logic to hide, but good to revert for now
        if (hasStatusChange) {
            $iconArea.append($("<span>")
                    .attr("role", "img")
                    .attr("title", action.statusChange ? statusMessages[action.status].title : "edit")
                    .addClass("status-image ")
                    .addClass(statusMessages[action.status].iconPath).css({verticalAlign: "top"}))
        }

        const iconLine = (line, $elm) => line == 1 && hasStatusChange
                ? $iconArea.append(
                        $elm.css({
                            width: "calc(100% - 48px)", display: "inline-block", textIndent: -16, marginLeft: 16,
                            marginTop: 8, marginBottom: 0,
                            verticalAlign: "top"})
                )
                : $iconArea.append($("<div>").css({width: "calc(100% - 45px)", textIndent: -16, marginLeft: 48, marginBottom: 4})
                        .append($elm))
        let lineNumber = 1;

        const actionDefName = this.describeAction(action.actionId);
        const $actionName = $("<span>").text(actionDefName).css("font-style", "italic");
        const $actionUuid = $("<span>").text(' [' + action.actionInstanceUuid + ']').addClass("small actions-id").hide();
        if (hasStatusChange) {
            iconLine(lineNumber, $actionName).append($actionUuid);
        } else {
            $entry.append($actionName).append($actionUuid);
        }
        lineNumber++;

        if (action.goalName) {
            const messages = this.serviceRecipient.features.getMessages();
            const label = messages[`${this.work.getDto().taskName}.goalName.label`] || messages["goalName.label"]
            iconLine(lineNumber, $("<span>").text(label + ": " + action.goalName))
            lineNumber++
        }
        if (action.targetSchedule) {
            // we hide the " due " using 'null' targetDate - so we simply show the schedule information
            const schedule = ScheduleData.fromTargetSchedule(null, action.targetSchedule)
            iconLine(lineNumber, $("<span>").text("schedule: " + schedule.asText(true)));
            lineNumber++;
            // TODO show the newly scheduled datetime?
        } else if (action.targetDateTime) {
            const taskDate = EccoDateTime.parseIso8601(action.targetDateTime).toEccoDate();
            iconLine(lineNumber, $("<span>").text("target: " + taskDate.formatPretty()))
            lineNumber++
        }
        if (action.expiryDate) {
            const expiryDte = EccoDate.parseIso8601(action.expiryDate).toDateTimeMidnight()
            iconLine(lineNumber, $("<span>").text("expiry: " + expiryDte.formatDateTimePretty(true)))
            lineNumber++
        }
        if (action.score) {
            if (this.serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(this.work.getDto().taskName, "scoreListName")) {
                const scoreName = this.serviceRecipient.features.getListDefinitionEntryById(action.score).getFullName();
                iconLine(lineNumber, $("<span>").text("score: " + scoreName))
            } else {
                iconLine(lineNumber, $("<span>").text("score: " + action.score))
            }
            lineNumber++
        }
        if (action.statusChangeReasonId) {
            const reasonName = this.serviceRecipient.features.getListDefinitionEntryById(action.statusChangeReasonId).getFullName();
            iconLine(lineNumber, $("<span>").text("reason: " + reasonName))
            lineNumber++
        }
        if (action.goalPlan) {
            // NB need goalPlan.history.label, since goalPlan.label=comment. e.g. status or plan of action?
            // const messages = this.serviceRecipient.features.getMessages();
            // const label = messages[`${this.work.getDto().taskName}.goalPlan.label`] || messages["goalPlan.label"]
            iconLine(lineNumber, $("<span>").css({"white-space": "pre-wrap"}).text("plan: " + action.goalPlan))
            lineNumber++
        }

        // RISK SECTION
        if (action.likelihood > 0 && action.severity > 0) {
            const likelihood = this.serviceRecipient.features.getLikelihood(action.likelihood)
            const severity = this.serviceRecipient.features.getSeverity(action.severity)

            if (likelihood && severity) {
                // NB need a new label in messages for likelihood / severity
                iconLine(lineNumber, $("<span>").text("likelihood: " + likelihood.name))
                lineNumber++
                iconLine(lineNumber, $("<span>").text("severity: " + severity.name))
                lineNumber++
            }
        }
        if (action.hazard) {
            // NB need a new label in messages for trigger / control
            iconLine(lineNumber, $("<span>").css({"white-space": "pre-wrap"})
                    .text("trigger: " + action.hazard))
            lineNumber++
            // $li.append($('<p class="extra">')
            //         .css({"white-space": "pre-wrap"})
            //         .text("- control: " + action.intervention));
        }
        if (action.intervention) {
            iconLine(lineNumber, $("<span>").css({"white-space": "pre-wrap"})
                    .text("control: " + action.intervention))
            lineNumber++
        }
        // RISK SECTION

        $entry.append($iconArea)

        return $entry;
    }

    private flagMessages: { title: string; classes: string }[] = [
        {title: "removed flag", classes: "fa fa-flag risk-flag-off"},
        {title: "added flag", classes: "fa fa-flag risk-flag-on"}
    ];

    protected renderFlags(flags: FlagEvidenceDto[]) {
        const $list = $("<ul>").addClass("list-unstyled");
        flags && $list.append($("<ul>").addClass("list-unstyled")
            .append(flags.map(flag =>
                $("<li>")
                    .append($("<i>")
                        .addClass(this.flagMessages[flag.value ? 1 : 0].classes)
                        .attr("title", this.flagMessages[flag.value ? 1 : 0].title))
                    .append(document.createTextNode(" "))
                    .append($("<span>")
                        .text(this.flagMessages[flag.value ? 1 : 0].title + ": " + this.serviceRecipient.features.getListDefinitionEntryById(flag.flagId).getName()))
        )));
        return $list
    }

    protected associatedActions(associatedActions: number[], actions: {actionId: number}[]) {
        const $list = $("<ul>").addClass("list-unstyled");
        associatedActions && $list.append(associatedActions
            // Filter stuff already seen so we don't see link when adding or updating
            .filter(actionId => !actions.some(swa => swa.actionId == actionId))
            // TODO: filter out anything that was in work.actions, so was already shown
            .map(actionId => {
                const $entry = $("<li>");
                $entry.append($("<span>")
                        .attr("role", "img")
                        .addClass("status-image link") // linked-evidence
                        .css({"vertical-align": "middle"})
                        .attr("title", "linked"))
                $entry.append(_.escape(this.describeAction(actionId)));
                return $entry;
            }));
        return $list
    }

    protected describeAction(actionId: number) {return null}

    // RiskActionEvidenceDto currently extends SupportAction
    protected sortedActions(actions: SupportAction[], outcomeIdsOrdered: number[]) {
        // NB sorting actions can reply on all outcomes -> actiongroups -> actions existing (since we do elsewhere)
        // but not all the outcomes may be in the config
        const sortedActions = actions && actions.sort((a, b) => {

            // if the outcome of a is after that of b... move it to the end
            // if the outcome does not exist, then it goes to the end, as it's -1 if not found
            const outcomeOrder = outcomeIdsOrdered.indexOf(a.outcomeId) - outcomeIdsOrdered.indexOf(b.outcomeId);
            if (outcomeOrder !== 0) {
                return outcomeOrder;
            }
            // the outcomes are the same, but it may or may not exist - depending if it's in the config
            const outcome = this.serviceRecipient.features.getAnyOutcomeById(a.outcomeId);

            // if the actiongroup of a is after that of b... move it to the end
            const actionGroupIdsOrdered = outcome.actionGroups.map(ag => ag.id);
            const actionGroupOrder = actionGroupIdsOrdered.indexOf(a.actionGroupId) - actionGroupIdsOrdered.indexOf(b.actionGroupId);
            if (actionGroupOrder !== 0) {
                return actionGroupOrder;
            }
            const actionGroup = this.serviceRecipient.features.getAnyActionById(a.actionId).getActionGroupComponent();

            // if the actiongroups are the same
            // if the action of a is after that of b... move it to the end
            const actionIdsOrdered = actionGroup.getActions().map(a => a.getId());
            const actionOrder = actionIdsOrdered.indexOf(a.actionId) - actionIdsOrdered.indexOf(b.actionId);
            if (actionOrder !== 0) {
                return actionOrder;
            }

            // if the hierarchy is after
            const hierarchyOrder = a.hierarchy - b.hierarchy;
            if (hierarchyOrder !== 0) {
                return hierarchyOrder;
            }

            // if the position is after
            const positionOrder = new HierarchyPosition(a.position).orderby() - new HierarchyPosition(b.position).orderby();
            if (positionOrder !== 0) {
                return positionOrder;
            }

            return 0;
        }) || [];
        return sortedActions;
    }
}
