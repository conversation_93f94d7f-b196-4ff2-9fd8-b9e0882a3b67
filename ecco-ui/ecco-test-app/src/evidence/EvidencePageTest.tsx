import * as React from "react";
import {FC} from "react";
import {Command} from "ecco-commands";
import {EvidencePageLayout, EvidencePageLoaderForCommandForm} from "ecco-evidence";
import {CommandForm} from "ecco-components";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";

const EvidencePageTestLayout: FC<{cmdEmitted?: Command[]; cmdEmittedDraft: Command[]}> = props => {
    return (
        <>
            <EvidencePageLayout />
            <CommandFormTestOutput
                cmdEmitted={props.cmdEmitted || []}
                cmdEmittedDraft={props.cmdEmittedDraft}
            />
        </>
    );
};

/**
 * Test component using commands and optional modal
 * modal gives us the ability to have sticky save/cancel buttons
 */
export const EvidencePageCommandFormTest: FC<{srId: number; taskName: string}> = props => {
    return (
        <CommandFormTest>
            {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                /* -- Use this for live testing (which is what ecco-test-app is about, check usages) -- */
                /* -- NB see EvidencePage*Test for specific cypress testing -- */
                <EvidencePageLoaderForCommandForm
                    srId={props.srId}
                    taskName={props.taskName}
                    readOnly={false}
                >
                    <EvidencePageTestLayout
                            cmdEmitted={cmdEmitted}
                            cmdEmittedDraft={cmdEmittedDraft}
                    />
                </EvidencePageLoaderForCommandForm>

                /*
                -- Use this for hard-coded data --
                -- also see TestServicesContextProvider for cypress tests in respective modules
                <EvidencePageSetupForCommandForm
                    initData={testEvidencePageData(props.srId, props.taskName)}
                    actions={evidencePageReducer}>
                    <EvidencePageTestLayout cmdEmitted={cmdEmitted} />
                </EvidencePageSetupForCommandForm>
                */
            )}
        </CommandFormTest>
    );
};

/**
 * Default init data.
 * Unlike SmartStepTest, where the test creates/holds the props, the EvidencePageContextProvider
 * creates/holds the props, so unless we use the provider directly, we create an EvidencePageData
 * NB We could use a real API
 * NB We could also mock the actions by replacing evidencePageReducer above
 */
/* -- Use this for hard-coded data --*/
/*
function testEvidencePageData(srId: number, taskName: string): EvidencePageData {
    return {
        sessionData: sessionData,
        serviceId: 1,
        taskName: taskName,
        evidenceDef: EvidenceDef.fromTaskName(sessionData, serviceType, taskName), // refactor out
        configResolver: configResolver(), // refactor out
        workUuid: Uuid.randomV4(),
        workUuid2: Uuid.randomV4(),
        serviceRecipientId: srId,
        readOnly: false,
        comment: undefined,
        workDate: undefined,
        commentTypeId: undefined,
        commentTypes: [
            {id: 1, name: "item 1"},
            {id: 2, name: "item 2"}
        ],
        clientStatusId: undefined,
        meetingStatusId: undefined,
        supportActions: [],
        questionAnswers: []
    };
}
*/
