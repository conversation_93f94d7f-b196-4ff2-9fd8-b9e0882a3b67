import * as React from "react";
import {useState} from "react";
import {
    CircularProgress,
    Box,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemText,
    Divider
} from "@eccosolutions/ecco-mui";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useBuildingsWithOccupancyHistory} from "../data/entityLoadHooks";

/**
 * Component for displaying occupancy lists
 */
export const OccupancyList: React.FC = () => {
    const [pageNumber, setPageNumber] = useState<number>(0);
    const [from, setFrom] = useState<EccoDate>(EccoDate.todayLocalTime());
    const [to, setTo] = useState<EccoDate>(EccoDate.todayLocalTime());

    const {bldgsWithOccupancy, error, loading} = useBuildingsWithOccupancyHistory(
        from,
        to,
        pageNumber
    );

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box p={2}>
                <Typography color="error">error loading data: {error.message}</Typography>
            </Box>
        );
    }

    if (!bldgsWithOccupancy || bldgsWithOccupancy.length === 0) {
        return (
            <Box p={2}>
                <Typography>no units found</Typography>
            </Box>
        );
    }

    return (
        <Box p={2}>
            {bldgsWithOccupancy.map(building => (
                <Card key={building.buildingId} /*sx={{ mb: 2 }}*/>
                    <CardContent>
                        <Typography variant="h6" component="h2" gutterBottom>
                            {building.name || `Building ${building.buildingId}`}
                        </Typography>

                        {building.occupancyHistory && building.occupancyHistory.length > 0 ? (
                            <List>
                                {building.occupancyHistory.map((occupancy, index) => (
                                    <React.Fragment key={occupancy.id}>
                                        <ListItem>
                                            <ListItemText
                                                primary={`Service Recipient ID: ${occupancy.serviceRecipientId}`}
                                                secondary={
                                                    <>
                                                        <Typography
                                                            component="span"
                                                            variant="body2"
                                                        >
                                                            From:{" "}
                                                            {new Date(
                                                                occupancy.validFrom
                                                            ).toLocaleDateString()}
                                                        </Typography>
                                                        {occupancy.validTo && (
                                                            <>
                                                                <br />
                                                                <Typography
                                                                    component="span"
                                                                    variant="body2"
                                                                >
                                                                    To:{" "}
                                                                    {new Date(
                                                                        occupancy.validTo
                                                                    ).toLocaleDateString()}
                                                                </Typography>
                                                            </>
                                                        )}
                                                    </>
                                                }
                                            />
                                        </ListItem>
                                        {index < building.occupancyHistory!.length - 1 && (
                                            <Divider />
                                        )}
                                    </React.Fragment>
                                ))}
                            </List>
                        ) : (
                            <Typography variant="body2" color="textSecondary">
                                No occupancy history for this building.
                            </Typography>
                        )}
                    </CardContent>
                </Card>
            ))}
        </Box>
    );
};
