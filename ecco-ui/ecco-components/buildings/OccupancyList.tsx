import * as React from "react";
import {useState} from "react";
import {
    CircularProgress,
    Box,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemText,
    Divider
} from "@eccosolutions/ecco-mui";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useBuildingsWithOccupancyHistory} from "../data/entityLoadHooks";

interface OccupancyHistorySectionProps {
    occupancyHistory?: Array<{
        id: number;
        serviceRecipientId: number;
        validFrom: string;
        validTo?: string;
    }>;
}

/**
 * Component for rendering a single occupancy record
 */
const OccupancyItem: React.FC<{ occupancy: { id: string; serviceRecipientId: string; validFrom: string; validTo?: string; } }> = ({ occupancy }) => (
    <ListItem>
        <ListItemText
            primary={`Service Recipient ID: ${occupancy.serviceRecipientId}`}
            secondary={
                <>
                    <Typography component="span" variant="body2">
                        From: {new Date(occupancy.validFrom).toLocaleDateString()}
                    </Typography>
                    {occupancy.validTo && (
                        <>
                            <br />
                            <Typography component="span" variant="body2">
                                To:{" "}
                                {new Date(occupancy.validTo).toLocaleDateString()}
                            </Typography>
                        </>
                    )}
                </>
            }
        />
    </ListItem>
);

/**
 * Component for displaying a section of occupancy records with a title
 */
const OccupancySection: React.FC<{ title: string; occupancies: Array<{ id: string; serviceRecipientId: string; validFrom: string; validTo?: string; }>; color?: string }> = ({ title, occupancies, color }) => {
    if (occupancies.length === 0) {
        return null;
    }

    return (
        <Box mb={2}>
            <Typography variant="subtitle2" component="h3" gutterBottom sx={{ color: color || 'inherit', fontWeight: 'bold' }}>
                {title}
            </Typography>
            <List dense>
                {occupancies.map((occupancy, index) => (
                    <React.Fragment key={occupancy.id}>
                        <OccupancyItem occupancy={occupancy} />
                        {index < occupancies.length - 1 && <Divider />}
                    </React.Fragment>
                ))}
            </List>
        </Box>
    );
};

/**
 * Component for displaying occupancy history for a building split into past, current, and future
 */
const OccupancyHistorySection: React.FC<OccupancyHistorySectionProps> = ({occupancyHistory}) => {
    if (!occupancyHistory || occupancyHistory.length === 0) {
        return (
            <Typography variant="body2" color="textSecondary">
                -
            </Typography>
        );
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Categorize occupancy records
    const pastOccupancies = occupancyHistory.filter(occupancy => {
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        return validTo && validTo < today;
    });

    const currentOccupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        return validFrom <= today && (!validTo || validTo >= today);
    });

    const futureOccupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);
        return validFrom > today;
    });

    return (
        <Box>
            <OccupancySection
                title="Current Occupancy"
                occupancies={currentOccupancies}
                color="success.main"
            />
            <OccupancySection
                title="Future Occupancy"
                occupancies={futureOccupancies}
                color="info.main"
            />
            <OccupancySection
                title="Past Occupancy"
                occupancies={pastOccupancies}
                color="text.secondary"
            />
        </Box>
    );
};

/**
 * Component for displaying occupancy lists
 */
export const OccupancyList: React.FC = () => {
    const [pageNumber, setPageNumber] = useState<number>(0);
    const [from, setFrom] = useState<EccoDate>(EccoDate.todayLocalTime());
    const [to, setTo] = useState<EccoDate>(EccoDate.todayLocalTime());

    const {bldgsWithOccupancy, error, loading} = useBuildingsWithOccupancyHistory(
        from,
        to,
        pageNumber
    );

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box p={2}>
                <Typography color="error">error loading data: {error.message}</Typography>
            </Box>
        );
    }

    if (!bldgsWithOccupancy || bldgsWithOccupancy.length === 0) {
        return (
            <Box p={2}>
                <Typography>no units found</Typography>
            </Box>
        );
    }

    return (
        <Box p={2}>
            {bldgsWithOccupancy.map(building => (
                <Card key={building.buildingId} /*sx={{ mb: 2 }}*/>
                    <CardContent>
                        <Typography variant="h6" component="h2" gutterBottom>
                            {building.name || `b-${building.buildingId}`}
                        </Typography>

                        <OccupancyHistorySection occupancyHistory={building.occupancyHistory} />
                    </CardContent>
                </Card>
            ))}
        </Box>
    );
};
